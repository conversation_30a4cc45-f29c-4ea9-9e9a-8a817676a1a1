import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:path/path.dart' as path;

// Import your app's services
import 'my_flutter_app/lib/models/models.dart';
import 'my_flutter_app/lib/services/firebase_storage_service.dart';
import 'my_flutter_app/lib/services/realtime_database_service.dart';

void main() async {
  print('Starting PLR Content Upload Script...');
  
  try {
    // Initialize Firebase
    await Firebase.initializeApp();
    print('Firebase initialized successfully');

    final ContentUploadScript uploader = ContentUploadScript();
    
    // Upload content from the content directory
    await uploader.uploadAllContent();
    
    print('Content upload completed successfully!');
  } catch (e) {
    print('Error during upload: $e');
    exit(1);
  }
}

class ContentUploadScript {
  final FirebaseStorageService _storageService = FirebaseStorageService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  Future<void> uploadAllContent() async {
    final String contentPath = 'content';
    final Directory contentDir = Directory(contentPath);
    
    if (!await contentDir.exists()) {
      throw Exception('Content directory not found: $contentPath');
    }

    // Define the content structure we want to upload
    final Map<String, List<String>> contentStructure = {
      'Business': [
        'content/AffCommissionFormula/AffCommissionFormula/eBook/Report/5 Ways To Increase Your Commission As An Affiliate Marketer - Report.pdf',
        'content/AffiliateCashMastery/AffiliateCashMastery',
      ],
      'Technology': [
        'content/AIForProductivity/AIForProductivity/eBook/1 - Ebook/AI for Productivity.pdf',
        'content/AIForProductivity/AIForProductivity/Video',
      ],
      'Health': [
        // We'll add some recipe content here
        'content/12,000 Recipes bonus-20250715T102342Z-1-001',
      ],
    };

    for (final String category in contentStructure.keys) {
      print('\n=== Processing Category: $category ===');
      
      final List<String> paths = contentStructure[category]!;
      final List<ContentItem> categoryItems = [];

      for (final String contentPath in paths) {
        try {
          final List<ContentItem> items = await _processContentPath(contentPath, category);
          categoryItems.addAll(items);
        } catch (e) {
          print('Error processing $contentPath: $e');
        }
      }

      // Upload all items for this category
      if (categoryItems.isNotEmpty) {
        await _dbService.addMultipleContentItems(categoryItems);
        print('Added ${categoryItems.length} items to database for category: $category');
      }
    }
  }

  Future<List<ContentItem>> _processContentPath(String contentPath, String category) async {
    final FileSystemEntity entity = FileSystemEntity.typeSync(contentPath) == FileSystemEntityType.file
        ? File(contentPath)
        : Directory(contentPath);

    if (entity is File) {
      return await _processFile(entity, category);
    } else if (entity is Directory) {
      return await _processDirectory(entity, category);
    }

    return [];
  }

  Future<List<ContentItem>> _processFile(File file, String category) async {
    if (!await file.exists()) {
      print('File not found: ${file.path}');
      return [];
    }

    final String fileName = path.basename(file.path);
    final String extension = path.extension(fileName).toLowerCase();

    // Check if it's a supported file type
    if (!_isSupportedFileType(extension)) {
      print('Skipping unsupported file type: $fileName');
      return [];
    }

    try {
      print('Uploading file: $fileName');
      
      // Upload to Firebase Storage
      final String downloadUrl = await _storageService.uploadFile(
        file: file,
        category: category,
        fileName: fileName,
        onProgress: (progress) {
          stdout.write('\r  Progress: ${(progress * 100).toStringAsFixed(1)}%');
        },
      );
      
      print('\n  Upload completed: $fileName');

      // Create content item
      final ContentItem item = _createContentItem(
        file: file,
        category: category,
        downloadUrl: downloadUrl,
      );

      return [item];
    } catch (e) {
      print('Failed to upload $fileName: $e');
      return [];
    }
  }

  Future<List<ContentItem>> _processDirectory(Directory directory, String category) async {
    if (!await directory.exists()) {
      print('Directory not found: ${directory.path}');
      return [];
    }

    final List<ContentItem> items = [];
    
    // Get all files in directory recursively
    await for (final FileSystemEntity entity in directory.list(recursive: true)) {
      if (entity is File) {
        final String fileName = path.basename(entity.path);
        final String extension = path.extension(fileName).toLowerCase();
        
        if (_isSupportedFileType(extension)) {
          final List<ContentItem> fileItems = await _processFile(entity, category);
          items.addAll(fileItems);
          
          // Limit to prevent overwhelming the system
          if (items.length >= 10) {
            print('Limiting to 10 files per directory for demo purposes');
            break;
          }
        }
      }
    }

    return items;
  }

  bool _isSupportedFileType(String extension) {
    const List<String> supportedTypes = ['.pdf', '.mp4', '.txt', '.doc', '.docx', '.mov', '.avi', '.mkv'];
    return supportedTypes.contains(extension);
  }

  ContentItem _createContentItem({
    required File file,
    required String category,
    required String downloadUrl,
  }) {
    final String fileName = path.basename(file.path);
    final String fileNameWithoutExt = path.basenameWithoutExtension(file.path);
    final String extension = path.extension(file.path);
    final int fileSize = file.lengthSync();

    // Generate a unique ID
    final String id = _generateContentId(fileName, category);

    // Create a title from the filename
    final String title = _generateTitle(fileNameWithoutExt);

    // Generate a description
    final String description = _generateDescription(title, category, extension);

    return ContentItem(
      id: id,
      title: title,
      description: description,
      category: category,
      fileUrl: downloadUrl,
      fileName: fileName,
      fileType: extension.replaceFirst('.', ''),
      fileSize: fileSize,
      createdAt: DateTime.now(),
    );
  }

  String _generateContentId(String fileName, String category) {
    final String sanitized = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');
    
    return '${category.toLowerCase()}_${sanitized}_${DateTime.now().millisecondsSinceEpoch}';
  }

  String _generateTitle(String fileNameWithoutExt) {
    return fileNameWithoutExt
        .replaceAll(RegExp(r'[_-]'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' ')
        .trim();
  }

  String _generateDescription(String title, String category, String extension) {
    final String fileType = _getFileTypeDescription(extension);
    
    return 'A $fileType resource about $title in the $category category. '
           'This PLR/MRR content is ready for download and use.';
  }

  String _getFileTypeDescription(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'PDF document';
      case '.mp4':
      case '.mov':
      case '.avi':
      case '.mkv':
        return 'video';
      case '.txt':
        return 'text document';
      case '.doc':
      case '.docx':
        return 'Word document';
      default:
        return 'file';
    }
  }
}
