{"logs": [{"outputFile": "com.example.my_flutter_app-mergeDebugResources-32:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7976d4e64729cb9c47971e21b0850b04\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "88,89,90,91,92,93,94,95,374,375,376,377,378,379,380,381,383,384,385,386,387,388,389,390,391,2812,3041", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4081,4171,4251,4341,4431,4511,4592,4672,22739,22844,23025,23150,23257,23437,23560,23676,23946,24134,24239,24420,24545,24720,24868,24931,24993,159278,166534", "endLines": "88,89,90,91,92,93,94,95,374,375,376,377,378,379,380,381,383,384,385,386,387,388,389,390,391,2824,3059", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4166,4246,4336,4426,4506,4587,4667,4747,22839,23020,23145,23252,23432,23555,23671,23774,24129,24234,24415,24540,24715,24863,24926,24988,25067,159588,166946"}}, {"source": "E:\\Fiverr_Projects\\my_flutter_app\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,199,318,400,504,613,733,836", "endColumns": "143,118,81,103,108,119,102,71", "endOffsets": "194,313,395,499,608,728,831,903"}, "to": {"startLines": "393,397,398,399,400,401,402,403", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "25140,25499,25618,25700,25804,25913,26033,26136", "endColumns": "143,118,81,103,108,119,102,71", "endOffsets": "25279,25613,25695,25799,25908,26028,26131,26203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,4,10,18,29,41,47,53,54,55,56,57,301,2032,2038,3072,3080,3095", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,392,611,984,1298,1486,1673,1726,1786,1838,1883,18266,132723,132918,167263,167545,168159", "endLines": "2,9,17,25,40,46,52,53,54,55,56,57,301,2037,2042,3079,3094,3110", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,387,606,825,1293,1481,1668,1721,1781,1833,1878,1917,18321,132913,133071,167540,168154,168808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4a06d06ec0ce1c73cc8df2ae11f0f21f\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "3,26,27,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,84,85,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,232,233,234,235,236,237,238,254,255,256,257,258,259,260,261,297,298,299,300,303,306,307,310,327,333,334,335,336,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,404,406,407,408,409,410,411,419,420,424,428,432,437,443,450,454,458,463,467,471,475,479,483,487,493,497,503,507,513,517,522,526,529,533,539,543,549,553,559,562,566,570,574,578,582,583,584,585,588,591,594,597,601,602,603,604,605,608,610,612,614,619,620,624,630,634,635,637,649,650,654,660,664,665,666,670,697,701,702,706,734,906,932,1103,1129,1160,1168,1174,1190,1212,1217,1222,1232,1241,1250,1254,1261,1280,1287,1288,1297,1300,1303,1307,1311,1315,1318,1319,1324,1329,1339,1344,1351,1357,1358,1361,1365,1370,1372,1374,1377,1380,1382,1386,1389,1396,1399,1402,1406,1408,1412,1414,1416,1418,1422,1430,1438,1450,1456,1465,1468,1479,1482,1483,1488,1489,1501,1570,1640,1641,1651,1660,1661,1663,1667,1670,1673,1676,1679,1682,1685,1688,1692,1695,1698,1701,1705,1708,1712,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1738,1740,1741,1742,1743,1744,1745,1746,1747,1749,1750,1752,1753,1755,1757,1758,1760,1761,1762,1763,1764,1765,1767,1768,1769,1770,1771,1783,1785,1787,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1803,1804,1805,1806,1807,1808,1809,1811,1815,1819,1820,1821,1822,1823,1824,1828,1829,1830,1831,1833,1835,1837,1839,1841,1842,1843,1844,1846,1848,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1864,1865,1866,1867,1869,1871,1872,1874,1875,1877,1879,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1894,1895,1896,1897,1899,1900,1901,1902,1903,1905,1907,1909,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1928,2003,2006,2009,2012,2026,2043,2085,2088,2117,2144,2153,2217,2580,2590,2628,2656,2776,2800,2806,2825,2846,2970,2990,2996,3000,3006,3060,3131,3197,3217,3272,3284,3310", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "164,830,875,1922,1963,2018,2080,2144,2214,2275,2350,2426,2503,2741,2826,2908,2984,3060,3137,3215,3321,3427,3506,3835,3892,4752,4826,4901,4966,5032,5092,5153,5225,5298,5365,5433,5492,5551,5610,5669,5728,5782,5836,5889,5943,5997,6051,6237,6311,6390,6463,6537,6608,6680,6752,6825,6882,6940,7013,7087,7161,7236,7308,7381,7451,7522,7582,7643,7712,7781,7851,7925,8001,8065,8142,8218,8295,8360,8429,8506,8581,8650,8718,8795,8861,8922,9019,9084,9153,9252,9323,9382,9440,9497,9556,9620,9691,9763,9835,9907,9979,10046,10114,10182,10241,10304,10368,10458,10549,10609,10675,10742,10808,10878,10942,10995,11062,11123,11190,11303,11361,11424,11489,11554,11629,11702,11774,11818,11865,11911,11960,12021,12082,12143,12205,12269,12333,12397,12462,12525,12585,12646,12712,12771,12831,12893,12964,13024,13723,13809,13896,13986,14073,14161,14243,14326,14416,15485,15537,15595,15640,15706,15770,15827,15884,18061,18118,18166,18215,18383,18487,18534,18690,19595,19898,19962,20024,20084,20279,20353,20423,20501,20555,20625,20710,20758,20804,20865,20928,20994,21058,21129,21192,21257,21321,21382,21443,21495,21568,21642,21711,21786,21860,21934,22075,26208,26332,26410,26500,26588,26684,26774,27356,27445,27692,27973,28225,28510,28903,29380,29602,29824,30100,30327,30557,30787,31017,31247,31474,31893,32119,32544,32774,33202,33421,33704,33912,34043,34270,34696,34921,35348,35569,35994,36114,36390,36691,37015,37306,37620,37757,37888,37993,38235,38402,38606,38814,39085,39197,39309,39414,39531,39745,39891,40031,40117,40465,40553,40799,41217,41466,41548,41646,42303,42403,42655,43079,43334,43428,43517,43754,45778,46020,46122,46375,48531,59212,60728,71423,72951,74708,75334,75754,77015,78280,78536,78772,79319,79813,80418,80616,81196,82564,82939,83057,83595,83752,83948,84221,84477,84647,84788,84852,85217,85584,86260,86524,86862,87215,87309,87495,87801,88063,88188,88315,88554,88765,88884,89077,89254,89709,89890,90012,90271,90384,90571,90673,90780,90909,91184,91692,92188,93065,93359,93929,94078,94810,94982,95066,95402,95494,96122,101353,106724,106786,107364,107948,108039,108152,108381,108541,108693,108864,109030,109199,109366,109529,109772,109942,110115,110286,110560,110759,110964,111294,111378,111474,111570,111668,111768,111870,111972,112074,112176,112278,112378,112474,112586,112715,112838,112969,113100,113198,113312,113406,113546,113680,113776,113888,113988,114104,114200,114312,114412,114552,114688,114852,114982,115140,115290,115431,115575,115710,115822,115972,116100,116228,116364,116496,116626,116756,116868,117766,117912,118056,118194,118260,118350,118426,118530,118620,118722,118830,118938,119038,119118,119210,119308,119418,119470,119548,119654,119746,119850,119960,120082,120245,120402,120482,120582,120672,120782,120872,121113,121207,121313,121405,121505,121617,121731,121847,121963,122057,122171,122283,122385,122505,122627,122709,122813,122933,123059,123157,123251,123339,123451,123567,123689,123801,123976,124092,124178,124270,124382,124506,124573,124699,124767,124895,125039,125167,125236,125331,125446,125559,125658,125767,125878,125989,126090,126195,126295,126425,126516,126639,126733,126845,126931,127035,127131,127219,127337,127441,127545,127671,127759,127867,127967,128057,128167,128251,128353,128437,128491,128555,128661,128747,128857,128941,129200,131816,131934,132049,132129,132490,133076,134480,134558,135902,137263,137651,140494,150547,150885,152556,153913,158065,158816,159078,159593,159972,164250,164856,165085,165236,165451,166951,169351,172377,173121,175252,175592,176903", "endLines": "3,26,27,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,84,85,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,232,233,234,235,236,237,238,254,255,256,257,258,259,260,261,297,298,299,300,303,306,307,310,327,333,334,335,336,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,404,406,407,408,409,410,418,419,423,427,431,436,442,449,453,457,462,466,470,474,478,482,486,492,496,502,506,512,516,521,525,528,532,538,542,548,552,558,561,565,569,573,577,581,582,583,584,587,590,593,596,600,601,602,603,604,607,609,611,613,618,619,623,629,633,634,636,648,649,653,659,663,664,665,669,696,700,701,705,733,905,931,1102,1128,1159,1167,1173,1189,1211,1216,1221,1231,1240,1249,1253,1260,1279,1286,1287,1296,1299,1302,1306,1310,1314,1317,1318,1323,1328,1338,1343,1350,1356,1357,1360,1364,1369,1371,1373,1376,1379,1381,1385,1388,1395,1398,1401,1405,1407,1411,1413,1415,1417,1421,1429,1437,1449,1455,1464,1467,1478,1481,1482,1487,1488,1493,1569,1639,1640,1650,1659,1660,1662,1666,1669,1672,1675,1678,1681,1684,1687,1691,1694,1697,1700,1704,1707,1711,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1737,1739,1740,1741,1742,1743,1744,1745,1746,1748,1749,1751,1752,1754,1756,1757,1759,1760,1761,1762,1763,1764,1766,1767,1768,1769,1770,1771,1784,1786,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1802,1803,1804,1805,1806,1807,1808,1810,1814,1818,1819,1820,1821,1822,1823,1827,1828,1829,1830,1832,1834,1836,1838,1840,1841,1842,1843,1845,1847,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1863,1864,1865,1866,1868,1870,1871,1873,1874,1876,1878,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1893,1894,1895,1896,1898,1899,1900,1901,1902,1904,1906,1908,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,2002,2005,2008,2011,2025,2031,2052,2087,2116,2143,2152,2216,2579,2583,2617,2655,2673,2799,2805,2811,2845,2969,2989,2995,2999,3005,3040,3071,3196,3216,3271,3283,3309,3316", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "214,870,919,1958,2013,2075,2139,2209,2270,2345,2421,2498,2576,2821,2903,2979,3055,3132,3210,3316,3422,3501,3581,3887,3945,4821,4896,4961,5027,5087,5148,5220,5293,5360,5428,5487,5546,5605,5664,5723,5777,5831,5884,5938,5992,6046,6100,6306,6385,6458,6532,6603,6675,6747,6820,6877,6935,7008,7082,7156,7231,7303,7376,7446,7517,7577,7638,7707,7776,7846,7920,7996,8060,8137,8213,8290,8355,8424,8501,8576,8645,8713,8790,8856,8917,9014,9079,9148,9247,9318,9377,9435,9492,9551,9615,9686,9758,9830,9902,9974,10041,10109,10177,10236,10299,10363,10453,10544,10604,10670,10737,10803,10873,10937,10990,11057,11118,11185,11298,11356,11419,11484,11549,11624,11697,11769,11813,11860,11906,11955,12016,12077,12138,12200,12264,12328,12392,12457,12520,12580,12641,12707,12766,12826,12888,12959,13019,13087,13804,13891,13981,14068,14156,14238,14321,14411,14502,15532,15590,15635,15701,15765,15822,15879,15933,18113,18161,18210,18261,18412,18529,18578,18731,19622,19957,20019,20079,20136,20348,20418,20496,20550,20620,20705,20753,20799,20860,20923,20989,21053,21124,21187,21252,21316,21377,21438,21490,21563,21637,21706,21781,21855,21929,22070,22140,26256,26405,26495,26583,26679,26769,27351,27440,27687,27968,28220,28505,28898,29375,29597,29819,30095,30322,30552,30782,31012,31242,31469,31888,32114,32539,32769,33197,33416,33699,33907,34038,34265,34691,34916,35343,35564,35989,36109,36385,36686,37010,37301,37615,37752,37883,37988,38230,38397,38601,38809,39080,39192,39304,39409,39526,39740,39886,40026,40112,40460,40548,40794,41212,41461,41543,41641,42298,42398,42650,43074,43329,43423,43512,43749,45773,46015,46117,46370,48526,59207,60723,71418,72946,74703,75329,75749,77010,78275,78531,78767,79314,79808,80413,80611,81191,82559,82934,83052,83590,83747,83943,84216,84472,84642,84783,84847,85212,85579,86255,86519,86857,87210,87304,87490,87796,88058,88183,88310,88549,88760,88879,89072,89249,89704,89885,90007,90266,90379,90566,90668,90775,90904,91179,91687,92183,93060,93354,93924,94073,94805,94977,95061,95397,95489,95767,101348,106719,106781,107359,107943,108034,108147,108376,108536,108688,108859,109025,109194,109361,109524,109767,109937,110110,110281,110555,110754,110959,111289,111373,111469,111565,111663,111763,111865,111967,112069,112171,112273,112373,112469,112581,112710,112833,112964,113095,113193,113307,113401,113541,113675,113771,113883,113983,114099,114195,114307,114407,114547,114683,114847,114977,115135,115285,115426,115570,115705,115817,115967,116095,116223,116359,116491,116621,116751,116863,117003,117907,118051,118189,118255,118345,118421,118525,118615,118717,118825,118933,119033,119113,119205,119303,119413,119465,119543,119649,119741,119845,119955,120077,120240,120397,120477,120577,120667,120777,120867,121108,121202,121308,121400,121500,121612,121726,121842,121958,122052,122166,122278,122380,122500,122622,122704,122808,122928,123054,123152,123246,123334,123446,123562,123684,123796,123971,124087,124173,124265,124377,124501,124568,124694,124762,124890,125034,125162,125231,125326,125441,125554,125653,125762,125873,125984,126085,126190,126290,126420,126511,126634,126728,126840,126926,127030,127126,127214,127332,127436,127540,127666,127754,127862,127962,128052,128162,128246,128348,128432,128486,128550,128656,128742,128852,128936,129056,131811,131929,132044,132124,132485,132718,133588,134553,135897,137258,137646,140489,150542,150677,152250,153908,154480,158811,159073,159273,159967,164245,164851,165080,165231,165446,166529,167258,172372,173116,175247,175587,176898,177101"}}, {"source": "E:\\Fiverr_Projects\\my_flutter_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1494,1498", "startColumns": "4,4", "startOffsets": "95772,95953", "endLines": "1497,1500", "endColumns": "12,12", "endOffsets": "95948,96117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "366", "startColumns": "4", "startOffsets": "22145", "endColumns": "82", "endOffsets": "22223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f660676bf4ab7e115492941cf8444d98\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "80,81,82,83,221,222,392,394,395,396", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3586,3644,3710,3773,13092,13163,25072,25284,25351,25430", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3639,3705,3768,3830,13158,13230,25135,25346,25425,25494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "328", "startColumns": "4", "startOffsets": "19627", "endColumns": "42", "endOffsets": "19665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "28,68,69,86,87,118,119,223,224,225,226,227,228,229,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,304,305,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,338,367,368,369,370,371,372,373,405,1772,1773,1777,1778,1782,1926,1927,2584,2618,2674,2707,2737,2770", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "924,2581,2653,3950,4015,6105,6174,13235,13305,13373,13445,13515,13576,13650,14507,14568,14629,14691,14755,14817,14878,14946,15046,15106,15172,15245,15314,15371,15423,15938,16010,16086,16151,16210,16269,16329,16389,16449,16509,16569,16629,16689,16749,16809,16869,16928,16988,17048,17108,17168,17228,17288,17348,17408,17468,17528,17587,17647,17707,17766,17825,17884,17943,18002,18417,18452,18736,18791,18854,18909,18967,19025,19086,19149,19206,19257,19307,19368,19425,19491,19525,19560,20209,22228,22295,22367,22436,22505,22579,22651,26261,117008,117125,117326,117436,117637,129061,129133,150682,152255,154485,156216,157216,157898", "endLines": "28,68,69,86,87,118,119,223,224,225,226,227,228,229,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,304,305,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,338,367,368,369,370,371,372,373,405,1772,1776,1777,1781,1782,1926,1927,2589,2627,2706,2727,2769,2775", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "979,2648,2736,4010,4076,6169,6232,13300,13368,13440,13510,13571,13645,13718,14563,14624,14686,14750,14812,14873,14941,15041,15101,15167,15240,15309,15366,15418,15480,16005,16081,16146,16205,16264,16324,16384,16444,16504,16564,16624,16684,16744,16804,16864,16923,16983,17043,17103,17163,17223,17283,17343,17403,17463,17523,17582,17642,17702,17761,17820,17879,17938,17997,18056,18447,18482,18786,18849,18904,18962,19020,19081,19144,19201,19252,19302,19363,19420,19486,19520,19555,19590,20274,22290,22362,22431,22500,22574,22646,22734,26327,117120,117321,117431,117632,117761,129128,129195,150880,152551,156211,156892,157893,158060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "337,382", "startColumns": "4,4", "startOffsets": "20141,23779", "endColumns": "67,166", "endOffsets": "20204,23941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "330", "startColumns": "4", "startOffsets": "19730", "endColumns": "53", "endOffsets": "19779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e60d7cc8f585e105683d15c0883739b4\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "308,329", "startColumns": "4,4", "startOffsets": "18583,19670", "endColumns": "41,59", "endOffsets": "18620,19725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0f448994e490bf59f20aa19226509e50\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2053,2069,2075,3111,3127", "startColumns": "4,4,4,4,4", "startOffsets": "133593,134018,134196,168813,169224", "endLines": "2068,2074,2084,3126,3130", "endColumns": "24,24,24,24,24", "endOffsets": "134013,134191,134475,169219,169346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "302,309,332,2728,2733", "startColumns": "4,4,4,4,4", "startOffsets": "18326,18625,19834,156897,157067", "endLines": "302,309,332,2732,2736", "endColumns": "56,64,63,24,24", "endOffsets": "18378,18685,19893,157062,157211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "331", "startColumns": "4", "startOffsets": "19784", "endColumns": "49", "endOffsets": "19829"}}]}]}