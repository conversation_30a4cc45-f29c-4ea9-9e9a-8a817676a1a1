# 📱 PLR/MRR Content App

A complete Flutter mobile application for browsing and downloading PLR (Private Label Rights) and MRR (Master Resale Rights) content. Users can browse content by categories, download files to their device, and manage their downloaded content offline.

## 🎯 Features

### ✅ **Authentication**

- Google Sign-In integration
- Email/Password authentication
- Firebase Authentication backend

### ✅ **Content Management**

- Browse content by categories (Business, Technology, Health, etc.)
- View detailed information about each content item
- Real-time content loading from Firebase

### ✅ **Download System**

- Download files to local device storage
- Progress tracking during downloads
- Automatic duplicate prevention
- Support for multiple file types (PDF, MP4, TXT, DOC, DOCX, MOV, AVI, MKV)

### ✅ **User Tracking**

- Track downloads per user in Firebase Realtime Database
- "My Downloads" section showing all downloaded content
- Download history with timestamps
- Local file verification

### ✅ **Offline Access**

- Downloaded files accessible without internet
- Open files with system default applications
- Local storage management

## 🏗️ Architecture

### **Frontend (Flutter)**

- **Screens**: Categories, Content List, Content Detail, My Downloads, Admin Panel
- **Services**: Authentication, Firebase Storage, Realtime Database, Download Management
- **Models**: ContentItem, UserDownload

### **Backend (Firebase)**

- **Authentication**: User management and security
- **Realtime Database**: Content metadata and user download tracking
- **Storage**: File hosting with public access URLs

## 🔧 Setup Instructions

### **Prerequisites**

1. Flutter SDK installed
2. Firebase project configured
3. Android Studio/VS Code with Flutter extensions

### **Firebase Configuration**

1. Create Firebase project at https://console.firebase.google.com
2. Enable Authentication (Google + Email/Password)
3. Enable Realtime Database
4. Enable Storage
5. Download `google-services.json` to `android/app/`

### **Installation**

```bash
# Clone and setup
cd my_flutter_app
flutter pub get

# Run the app
flutter run
```

## 🎮 User Flow

1. **Login** → User authenticates with Google or email/password
2. **Browse Categories** → View available content categories
3. **Select Category** → See all content in that category
4. **View Details** → Check content information and download
5. **Download** → File saved to device storage + tracked in database
6. **My Downloads** → Access all downloaded content offline
7. **Open Files** → Use system apps to view downloaded content

## 🚀 Content Upload

Use the included Node.js script to upload content:

```bash
# Install dependencies
npm install firebase-admin

# Upload content (requires service account key)
node firebase_uploader_fixed.js
```

---

**Built with Flutter 💙 | Powered by Firebase 🔥**
