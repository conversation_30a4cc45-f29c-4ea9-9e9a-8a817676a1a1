# 🔑 Get Firebase Service Account Key

To fix the authentication issue, you need to download a service account key from Firebase Console.

## 📋 Steps to Get Service Account Key

### 1. Open Firebase Console
Go to: https://console.firebase.google.com/project/myapp-59f81

### 2. Navigate to Project Settings
- Click the **gear icon** (⚙️) next to "Project Overview"
- Select **"Project settings"**

### 3. Go to Service Accounts Tab
- Click on the **"Service accounts"** tab
- You should see "Firebase Admin SDK" section

### 4. Generate New Private Key
- Click **"Generate new private key"** button
- A dialog will appear warning about keeping the key secure
- Click **"Generate key"**

### 5. Download the Key
- A JSON file will be downloaded (usually named something like `myapp-59f81-firebase-adminsdk-xxxxx-xxxxxxxxxx.json`)
- **Rename this file to:** `serviceAccountKey.json`
- **Move it to:** `E:\Fiverr_Projects\serviceAccountKey.json` (same folder as the upload script)

## 🚀 Run the Upload Script

Once you have the service account key in place:

```bash
node firebase_uploader_fixed.js
```

## 🔒 Security Note

**IMPORTANT:** 
- Never commit the `serviceAccountKey.json` file to version control
- Keep this file secure and private
- It provides full admin access to your Firebase project

## 🎯 What This Fixes

The service account key provides the necessary authentication for:
- ✅ Uploading files to Firebase Storage
- ✅ Writing data to Realtime Database
- ✅ Making files publicly accessible

## 📊 Expected Output

After running the script, you should see:
- ✅ Successful file uploads
- 📁 Files organized by category in Firebase Storage
- 📝 Metadata entries in Realtime Database
- 🎉 Upload completion message

## 🔍 Verify Upload

Check Firebase Console:
1. **Storage:** https://console.firebase.google.com/project/myapp-59f81/storage
2. **Database:** https://console.firebase.google.com/project/myapp-59f81/database

You should see:
- Files in `content/Business/`, `content/Technology/`, `content/Health/` folders
- Database entries under `content` node with metadata
