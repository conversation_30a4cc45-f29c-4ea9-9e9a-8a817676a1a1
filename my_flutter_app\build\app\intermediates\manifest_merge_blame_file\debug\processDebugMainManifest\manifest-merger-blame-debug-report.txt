1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_flutter_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="true"
45        android:icon="@mipmap/ic_launcher"
46        android:label="my_flutter_app" >
47        <activity
48            android:name="com.example.my_flutter_app.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <service
81-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
82            android:name="com.google.firebase.components.ComponentDiscoveryService"
82-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
83            android:directBootAware="true"
83-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
84            android:exported="false" >
84-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
85            <meta-data
85-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
86                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
86-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
88            <meta-data
88-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
89                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
89-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
91            <meta-data
91-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
92                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
92-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
94            <meta-data
94-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
95                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
95-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
97            <meta-data
97-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
98                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
98-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
100        </service>
101
102        <activity
102-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
103            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
103-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
104            android:excludeFromRecents="true"
104-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
105            android:exported="true"
105-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
106            android:launchMode="singleTask"
106-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
107            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
107-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
108            <intent-filter>
108-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
109                <action android:name="android.intent.action.VIEW" />
109-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
109-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
111-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
112                <category android:name="android.intent.category.BROWSABLE" />
112-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
112-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
113
114                <data
114-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
115                    android:host="firebase.auth"
116                    android:path="/"
117                    android:scheme="genericidp" />
118            </intent-filter>
119        </activity>
120        <activity
120-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
121            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
121-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
122            android:excludeFromRecents="true"
122-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
123            android:exported="true"
123-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
124            android:launchMode="singleTask"
124-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
125            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
125-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
126            <intent-filter>
126-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
127                <action android:name="android.intent.action.VIEW" />
127-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
127-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
129-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
130-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
131
132                <data
132-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
133                    android:host="firebase.auth"
134                    android:path="/"
135                    android:scheme="recaptcha" />
136            </intent-filter>
137        </activity>
138
139        <provider
139-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
140            android:name="com.google.firebase.provider.FirebaseInitProvider"
140-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
141            android:authorities="com.example.my_flutter_app.firebaseinitprovider"
141-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
142            android:directBootAware="true"
142-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
143            android:exported="false"
143-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
144            android:initOrder="100" />
144-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
145
146        <activity
146-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
147            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
147-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
148            android:excludeFromRecents="true"
148-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
149            android:exported="false"
149-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
150            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
150-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
151        <!--
152            Service handling Google Sign-In user revocation. For apps that do not integrate with
153            Google Sign-In, this service will never be started.
154        -->
155        <service
155-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
156            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
156-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
157            android:exported="true"
157-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
158            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
158-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
159            android:visibleToInstantApps="true" />
159-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
160
161        <activity
161-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
162            android:name="com.google.android.gms.common.api.GoogleApiActivity"
162-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
163            android:exported="false"
163-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
164            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
164-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
165
166        <uses-library
166-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
167            android:name="androidx.window.extensions"
167-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
168            android:required="false" />
168-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
169        <uses-library
169-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
170            android:name="androidx.window.sidecar"
170-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
171            android:required="false" />
171-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
172
173        <meta-data
173-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
174            android:name="com.google.android.gms.version"
174-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
175            android:value="@integer/google_play_services_version" />
175-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
176
177        <provider
177-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
178            android:name="androidx.startup.InitializationProvider"
178-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
179            android:authorities="com.example.my_flutter_app.androidx-startup"
179-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
180            android:exported="false" >
180-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
181            <meta-data
181-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
182                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
182-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
183                android:value="androidx.startup" />
183-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
184            <meta-data
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
185                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
186                android:value="androidx.startup" />
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
187        </provider>
188
189        <receiver
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
190            android:name="androidx.profileinstaller.ProfileInstallReceiver"
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
191            android:directBootAware="false"
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
192            android:enabled="true"
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
193            android:exported="true"
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
194            android:permission="android.permission.DUMP" >
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
196                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
197            </intent-filter>
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
199                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
202                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
205                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
206            </intent-filter>
207        </receiver>
208    </application>
209
210</manifest>
