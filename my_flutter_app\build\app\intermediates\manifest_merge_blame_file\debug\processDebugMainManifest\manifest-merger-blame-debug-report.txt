1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_flutter_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="true"
45        android:icon="@mipmap/ic_launcher"
46        android:label="my_flutter_app" >
47        <activity
48            android:name="com.example.my_flutter_app.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <provider
81-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
82            android:name="com.crazecoder.openfile.FileProvider"
82-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
83            android:authorities="com.example.my_flutter_app.fileProvider.com.crazecoder.openfile"
83-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
84            android:exported="false"
84-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
85            android:grantUriPermissions="true"
85-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
86            android:requestLegacyExternalStorage="true" >
86-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
87            <meta-data
87-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
88                android:name="android.support.FILE_PROVIDER_PATHS"
88-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
89                android:resource="@xml/filepaths" />
89-->[:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
90        </provider>
91
92        <service
92-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
93            android:name="com.google.firebase.components.ComponentDiscoveryService"
93-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
94            android:directBootAware="true"
94-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
95            android:exported="false" >
95-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
96            <meta-data
96-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
97                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
97-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
99            <meta-data
99-->[:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
100                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
100-->[:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
102            <meta-data
102-->[:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
103                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
103-->[:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
105            <meta-data
105-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
106                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
106-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
108            <meta-data
108-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
109                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
109-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
111            <meta-data
111-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
112                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
112-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
114            <meta-data
114-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
115                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
115-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
117            <meta-data
117-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
118                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
118-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
120            <meta-data
120-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
121                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
121-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
123            <meta-data
123-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
124                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
124-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
126            <meta-data
126-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
127                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
127-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
129            <meta-data
129-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
130                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
130-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
132            <meta-data
132-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
133                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
133-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
135        </service>
136
137        <activity
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
138            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
139            android:excludeFromRecents="true"
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
140            android:exported="true"
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
141            android:launchMode="singleTask"
141-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
142            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
143            <intent-filter>
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
144                <action android:name="android.intent.action.VIEW" />
144-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
144-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
145
146                <category android:name="android.intent.category.DEFAULT" />
146-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
146-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
147                <category android:name="android.intent.category.BROWSABLE" />
147-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
147-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
148
149                <data
149-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
150                    android:host="firebase.auth"
151                    android:path="/"
152                    android:scheme="genericidp" />
153            </intent-filter>
154        </activity>
155        <activity
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
156            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
157            android:excludeFromRecents="true"
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
158            android:exported="true"
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
159            android:launchMode="singleTask"
159-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
160            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
161            <intent-filter>
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
162                <action android:name="android.intent.action.VIEW" />
162-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
162-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
163
164                <category android:name="android.intent.category.DEFAULT" />
164-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
164-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
165                <category android:name="android.intent.category.BROWSABLE" />
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
166
167                <data
167-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
168                    android:host="firebase.auth"
169                    android:path="/"
170                    android:scheme="recaptcha" />
171            </intent-filter>
172        </activity>
173
174        <provider
174-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
175            android:name="com.google.firebase.provider.FirebaseInitProvider"
175-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
176            android:authorities="com.example.my_flutter_app.firebaseinitprovider"
176-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
177            android:directBootAware="true"
177-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
178            android:exported="false"
178-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
179            android:initOrder="100" />
179-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
180
181        <activity
181-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
182            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
182-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
183            android:excludeFromRecents="true"
183-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
184            android:exported="false"
184-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
185            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
185-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
186        <!--
187            Service handling Google Sign-In user revocation. For apps that do not integrate with
188            Google Sign-In, this service will never be started.
189        -->
190        <service
190-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
191            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
191-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
192            android:exported="true"
192-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
193            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
193-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
194            android:visibleToInstantApps="true" />
194-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
195
196        <activity
196-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
197            android:name="com.google.android.gms.common.api.GoogleApiActivity"
197-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
198            android:exported="false"
198-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
199            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
199-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
200
201        <uses-library
201-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
202            android:name="androidx.window.extensions"
202-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
203            android:required="false" />
203-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
204        <uses-library
204-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
205            android:name="androidx.window.sidecar"
205-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
206            android:required="false" />
206-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
207
208        <meta-data
208-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
209            android:name="com.google.android.gms.version"
209-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
210            android:value="@integer/google_play_services_version" />
210-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
211
212        <provider
212-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
213            android:name="androidx.startup.InitializationProvider"
213-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
214            android:authorities="com.example.my_flutter_app.androidx-startup"
214-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
215            android:exported="false" >
215-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
216            <meta-data
216-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
217                android:name="androidx.emoji2.text.EmojiCompatInitializer"
217-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
218                android:value="androidx.startup" />
218-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
219            <meta-data
219-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
220                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
221                android:value="androidx.startup" />
221-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
222            <meta-data
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
224                android:value="androidx.startup" />
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
225        </provider>
226
227        <receiver
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
228            android:name="androidx.profileinstaller.ProfileInstallReceiver"
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
229            android:directBootAware="false"
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
230            android:enabled="true"
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
231            android:exported="true"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
234                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
237                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
240                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
243                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
244            </intent-filter>
245        </receiver>
246    </application>
247
248</manifest>
