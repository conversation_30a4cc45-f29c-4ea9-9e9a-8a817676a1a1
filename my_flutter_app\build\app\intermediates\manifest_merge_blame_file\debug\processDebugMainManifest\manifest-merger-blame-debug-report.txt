1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_flutter_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission
16-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:4:5-5:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:4:22-78
18        android:maxSdkVersion="32" />
18-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:5:9-35
19    <uses-permission
19-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:6:5-7:38
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:6:22-77
21        android:maxSdkVersion="32" />
21-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:7:9-35
22    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- Android 13+ Media permissions -->
22-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:8:5-82
22-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:8:22-79
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:11:5-76
23-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:11:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:12:5-75
24-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:12:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:13:5-75
25-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:13:22-72
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:52:5-57:15
34        <intent>
34-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:53:9-56:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:54:13-72
35-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:54:21-70
36
37            <data android:mimeType="text/plain" />
37-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:55:13-50
37-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:55:19-48
38        </intent>
39    </queries>
40
41    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
41-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
41-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
42    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
42-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
42-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
43
44    <permission
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
51        android:name="android.app.Application"
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="true"
55        android:icon="@mipmap/ic_launcher"
56        android:label="PLR Content App" >
57        <activity
58            android:name="com.example.my_flutter_app.MainActivity"
59            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
60            android:exported="true"
61            android:hardwareAccelerated="true"
62            android:launchMode="singleTop"
63            android:taskAffinity=""
64            android:theme="@style/LaunchTheme"
65            android:windowSoftInputMode="adjustResize" >
66
67            <!--
68                 Specifies an Android theme to apply to this Activity as soon as
69                 the Android process has started. This theme is visible to the user
70                 while the Flutter UI initializes. After that, this theme continues
71                 to determine the Window background behind the Flutter UI.
72            -->
73            <meta-data
74                android:name="io.flutter.embedding.android.NormalTheme"
75                android:resource="@style/NormalTheme" />
76
77            <intent-filter>
78                <action android:name="android.intent.action.MAIN" />
79
80                <category android:name="android.intent.category.LAUNCHER" />
81            </intent-filter>
82        </activity>
83        <!--
84             Don't delete the meta-data below.
85             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
86        -->
87        <meta-data
88            android:name="flutterEmbedding"
89            android:value="2" />
90
91        <provider
91-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-25:20
92            android:name="com.crazecoder.openfile.FileProvider"
92-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-64
93            android:authorities="com.example.my_flutter_app.fileProvider.com.crazecoder.openfile"
93-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-88
94            android:exported="false"
94-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
95            android:grantUriPermissions="true" >
95-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-47
96            <meta-data
96-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-24:53
97                android:name="android.support.FILE_PROVIDER_PATHS"
97-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-67
98                android:resource="@xml/filepaths" />
98-->[:open_filex] E:\Fiverr_Projects\my_flutter_app\build\open_filex\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-50
99        </provider>
100
101        <service
101-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
102            android:name="com.google.firebase.components.ComponentDiscoveryService"
102-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
103            android:directBootAware="true"
103-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
104            android:exported="false" >
104-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
105            <meta-data
105-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
106                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
106-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
108            <meta-data
108-->[:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
109                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
109-->[:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
111            <meta-data
111-->[:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
112                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
112-->[:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
114            <meta-data
114-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
115                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
115-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
117            <meta-data
117-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
118                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
118-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
120            <meta-data
120-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
121                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
121-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
123            <meta-data
123-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
124                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
124-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
126            <meta-data
126-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
127                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
127-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
129            <meta-data
129-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
130                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
130-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
132            <meta-data
132-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
133                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
133-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
135            <meta-data
135-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
136                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
136-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
138            <meta-data
138-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
139                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
139-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
141            <meta-data
141-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
142                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
142-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
144        </service>
145
146        <activity
146-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
147            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
147-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
148            android:excludeFromRecents="true"
148-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
149            android:exported="false"
149-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
150            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
150-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
151        <!--
152            Service handling Google Sign-In user revocation. For apps that do not integrate with
153            Google Sign-In, this service will never be started.
154        -->
155        <service
155-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
156            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
156-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
157            android:exported="true"
157-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
158            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
158-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
159            android:visibleToInstantApps="true" />
159-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
160
161        <activity
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
162            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
162-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
163            android:excludeFromRecents="true"
163-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
164            android:exported="true"
164-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
165            android:launchMode="singleTask"
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
166            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
166-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
167            <intent-filter>
167-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
168                <action android:name="android.intent.action.VIEW" />
168-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
168-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
169
170                <category android:name="android.intent.category.DEFAULT" />
170-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
170-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
171                <category android:name="android.intent.category.BROWSABLE" />
171-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
171-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
172
173                <data
173-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:55:13-50
174                    android:host="firebase.auth"
175                    android:path="/"
176                    android:scheme="genericidp" />
177            </intent-filter>
178        </activity>
179        <activity
179-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
180            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
180-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
181            android:excludeFromRecents="true"
181-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
182            android:exported="true"
182-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
183            android:launchMode="singleTask"
183-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
184            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
184-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
185            <intent-filter>
185-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
186                <action android:name="android.intent.action.VIEW" />
186-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
186-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
187
188                <category android:name="android.intent.category.DEFAULT" />
188-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
188-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
189                <category android:name="android.intent.category.BROWSABLE" />
189-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
189-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
190
191                <data
191-->E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:55:13-50
192                    android:host="firebase.auth"
193                    android:path="/"
194                    android:scheme="recaptcha" />
195            </intent-filter>
196        </activity>
197
198        <provider
198-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
199            android:name="com.google.firebase.provider.FirebaseInitProvider"
199-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
200            android:authorities="com.example.my_flutter_app.firebaseinitprovider"
200-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
201            android:directBootAware="true"
201-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
202            android:exported="false"
202-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
203            android:initOrder="100" />
203-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
204
205        <activity
205-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
206            android:name="com.google.android.gms.common.api.GoogleApiActivity"
206-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
207            android:exported="false"
207-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
208            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
208-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
209
210        <uses-library
210-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
211            android:name="androidx.window.extensions"
211-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
212            android:required="false" />
212-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
213        <uses-library
213-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
214            android:name="androidx.window.sidecar"
214-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
215            android:required="false" />
215-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
216
217        <meta-data
217-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
218            android:name="com.google.android.gms.version"
218-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
219            android:value="@integer/google_play_services_version" />
219-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
220
221        <provider
221-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
222            android:name="androidx.startup.InitializationProvider"
222-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
223            android:authorities="com.example.my_flutter_app.androidx-startup"
223-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
224            android:exported="false" >
224-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
225            <meta-data
225-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
226                android:name="androidx.emoji2.text.EmojiCompatInitializer"
226-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
227                android:value="androidx.startup" />
227-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
228            <meta-data
228-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
229                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
229-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
230                android:value="androidx.startup" />
230-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
231            <meta-data
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
232                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
233                android:value="androidx.startup" />
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
234        </provider>
235
236        <receiver
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
237            android:name="androidx.profileinstaller.ProfileInstallReceiver"
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
238            android:directBootAware="false"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
239            android:enabled="true"
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
240            android:exported="true"
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
241            android:permission="android.permission.DUMP" >
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
243                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
246                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
247            </intent-filter>
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
249                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
250            </intent-filter>
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
252                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
253            </intent-filter>
254        </receiver>
255    </application>
256
257</manifest>
