{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Fiverr_Projects\\my_flutter_app\\build\\.cxx\\Debug\\1h2g6tz3\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Fiverr_Projects\\my_flutter_app\\build\\.cxx\\Debug\\1h2g6tz3\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}