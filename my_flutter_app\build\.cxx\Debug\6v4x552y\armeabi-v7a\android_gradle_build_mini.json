{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Fiverr_Projects\\my_flutter_app\\build\\.cxx\\Debug\\6v4x552y\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Fiverr_Projects\\my_flutter_app\\build\\.cxx\\Debug\\6v4x552y\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}