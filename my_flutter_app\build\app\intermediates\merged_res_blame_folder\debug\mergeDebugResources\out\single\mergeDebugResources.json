[{"merged": "com.example.my_flutter_app-debug-30:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-24:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-30:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-24:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-30:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-24:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-30:/drawable-v21_launch_background.xml.flat", "source": "com.example.my_flutter_app-main-24:/drawable-v21/launch_background.xml"}, {"merged": "com.example.my_flutter_app-debug-30:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-24:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-30:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-24:/mipmap-xxxhdpi/ic_launcher.png"}]