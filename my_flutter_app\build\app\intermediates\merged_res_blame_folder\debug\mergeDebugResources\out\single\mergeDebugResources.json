[{"merged": "com.example.my_flutter_app-debug-34:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-28:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-34:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-28:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-34:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-28:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-34:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-28:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.my_flutter_app-debug-34:/drawable-v21_launch_background.xml.flat", "source": "com.example.my_flutter_app-main-28:/drawable-v21/launch_background.xml"}, {"merged": "com.example.my_flutter_app-debug-34:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.my_flutter_app-main-28:/mipmap-hdpi/ic_launcher.png"}]