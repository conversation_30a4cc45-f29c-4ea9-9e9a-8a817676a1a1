import '../models/models.dart';
import '../services/realtime_database_service.dart';

class SampleDataCreator {
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  /// Create sample content for testing the app
  Future<void> createSampleContent() async {
    try {
      print('Creating sample PLR content...');

      final List<ContentItem> sampleItems = [
        // Business Category
        ContentItem(
          id: 'business_affiliate_marketing_guide',
          title: 'Affiliate Marketing Complete Guide',
          description: 'A comprehensive PDF guide covering all aspects of affiliate marketing, from beginner strategies to advanced techniques. Learn how to build profitable affiliate campaigns and maximize your commissions.',
          category: 'Business',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Faffiliate_marketing_guide.pdf?alt=media',
          fileName: 'affiliate_marketing_guide.pdf',
          fileType: 'pdf',
          fileSize: 2048000, // 2MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'business_email_marketing_secrets',
          title: 'Email Marketing Secrets',
          description: 'Discover the secrets of successful email marketing campaigns. This guide includes templates, strategies, and best practices for building and monetizing your email list.',
          category: 'Business',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Femail_marketing_secrets.pdf?alt=media',
          fileName: 'email_marketing_secrets.pdf',
          fileType: 'pdf',
          fileSize: 1536000, // 1.5MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'business_social_media_mastery',
          title: 'Social Media Marketing Mastery',
          description: 'Master social media marketing with this comprehensive video course. Learn platform-specific strategies for Facebook, Instagram, Twitter, and LinkedIn.',
          category: 'Business',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fsocial_media_mastery.mp4?alt=media',
          fileName: 'social_media_mastery.mp4',
          fileType: 'mp4',
          fileSize: 52428800, // 50MB
          createdAt: DateTime.now(),
        ),

        // Health Category
        ContentItem(
          id: 'health_nutrition_basics',
          title: 'Nutrition Basics for Healthy Living',
          description: 'Essential nutrition information for maintaining a healthy lifestyle. Includes meal planning guides, nutritional charts, and healthy recipe ideas.',
          category: 'Health',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fnutrition_basics.pdf?alt=media',
          fileName: 'nutrition_basics.pdf',
          fileType: 'pdf',
          fileSize: 3072000, // 3MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'health_fitness_workout_plan',
          title: '30-Day Fitness Workout Plan',
          description: 'A complete 30-day workout plan designed for all fitness levels. Includes exercise descriptions, progress tracking sheets, and nutrition tips.',
          category: 'Health',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Ffitness_workout_plan.pdf?alt=media',
          fileName: 'fitness_workout_plan.pdf',
          fileType: 'pdf',
          fileSize: 2560000, // 2.5MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'health_meditation_guide',
          title: 'Meditation and Mindfulness Guide',
          description: 'Learn the art of meditation and mindfulness with this comprehensive text guide. Includes various meditation techniques and daily practice routines.',
          category: 'Health',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fmeditation_guide.txt?alt=media',
          fileName: 'meditation_guide.txt',
          fileType: 'txt',
          fileSize: 512000, // 512KB
          createdAt: DateTime.now(),
        ),

        // Technology Category
        ContentItem(
          id: 'tech_ai_introduction',
          title: 'Introduction to Artificial Intelligence',
          description: 'A beginner-friendly introduction to AI concepts, applications, and future trends. Perfect for anyone looking to understand the basics of artificial intelligence.',
          category: 'Technology',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fai_introduction.pdf?alt=media',
          fileName: 'ai_introduction.pdf',
          fileType: 'pdf',
          fileSize: 1792000, // 1.75MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'tech_web_development_basics',
          title: 'Web Development Fundamentals',
          description: 'Learn the fundamentals of web development including HTML, CSS, and JavaScript. This guide provides a solid foundation for aspiring web developers.',
          category: 'Technology',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fweb_development_basics.pdf?alt=media',
          fileName: 'web_development_basics.pdf',
          fileType: 'pdf',
          fileSize: 4096000, // 4MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'tech_coding_tutorial_video',
          title: 'Python Programming Tutorial',
          description: 'A comprehensive video tutorial covering Python programming from basics to advanced concepts. Perfect for beginners and intermediate programmers.',
          category: 'Technology',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fpython_tutorial.mp4?alt=media',
          fileName: 'python_tutorial.mp4',
          fileType: 'mp4',
          fileSize: 104857600, // 100MB
          createdAt: DateTime.now(),
        ),

        // Education Category
        ContentItem(
          id: 'education_study_techniques',
          title: 'Effective Study Techniques',
          description: 'Proven study techniques and methods to improve learning efficiency and retention. Includes memory techniques, note-taking strategies, and exam preparation tips.',
          category: 'Education',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fstudy_techniques.pdf?alt=media',
          fileName: 'study_techniques.pdf',
          fileType: 'pdf',
          fileSize: 1280000, // 1.25MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'education_language_learning',
          title: 'Language Learning Strategies',
          description: 'Effective strategies for learning new languages quickly and efficiently. Includes practical tips, resources, and practice exercises.',
          category: 'Education',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Flanguage_learning.txt?alt=media',
          fileName: 'language_learning.txt',
          fileType: 'txt',
          fileSize: 768000, // 768KB
          createdAt: DateTime.now(),
        ),

        // Lifestyle Category
        ContentItem(
          id: 'lifestyle_productivity_hacks',
          title: 'Productivity Hacks for Busy People',
          description: 'Time-saving productivity hacks and life organization tips for busy professionals. Learn how to maximize your efficiency and achieve work-life balance.',
          category: 'Lifestyle',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fproductivity_hacks.pdf?alt=media',
          fileName: 'productivity_hacks.pdf',
          fileType: 'pdf',
          fileSize: 1024000, // 1MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'lifestyle_home_organization',
          title: 'Home Organization Masterclass',
          description: 'Transform your living space with these professional home organization techniques. Includes room-by-room guides and decluttering strategies.',
          category: 'Lifestyle',
          fileUrl: 'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fhome_organization.mp4?alt=media',
          fileName: 'home_organization.mp4',
          fileType: 'mp4',
          fileSize: 73400320, // 70MB
          createdAt: DateTime.now(),
        ),
      ];

      // Add all content items to the database
      await _dbService.addMultipleContentItems(sampleItems);
      
      print('Successfully created ${sampleItems.length} sample content items!');
      print('Categories created:');
      final Set<String> categories = sampleItems.map((item) => item.category).toSet();
      for (final category in categories) {
        final int count = sampleItems.where((item) => item.category == category).length;
        print('  - $category: $count items');
      }
    } catch (e) {
      print('Error creating sample content: $e');
      rethrow;
    }
  }

  /// Clear all existing content (use with caution!)
  Future<void> clearAllContent() async {
    try {
      print('Clearing all existing content...');
      
      // Get all categories
      final List<String> categories = await _dbService.getCategories();
      
      for (final category in categories) {
        final List<ContentItem> items = await _dbService.getContentByCategory(category);
        print('Found ${items.length} items in $category category');
        
        // Note: This would require implementing a delete method in the database service
        // For now, we'll just print what would be deleted
      }
      
      print('Content clearing completed');
    } catch (e) {
      print('Error clearing content: $e');
      rethrow;
    }
  }
}
