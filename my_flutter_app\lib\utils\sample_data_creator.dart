import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/models.dart';
import '../services/realtime_database_service.dart';
import '../services/firebase_storage_service.dart';

class SampleDataCreator {
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();
  final FirebaseStorageService _storageService = FirebaseStorageService();

  /// Create sample content for testing the app
  Future<void> createSampleContent() async {
    try {
      print('Creating sample PLR content...');

      final List<ContentItem> sampleItems = [
        // Business Category
        ContentItem(
          id: 'business_affiliate_marketing_guide',
          title: 'Affiliate Marketing Complete Guide',
          description:
              'A comprehensive PDF guide covering all aspects of affiliate marketing, from beginner strategies to advanced techniques. Learn how to build profitable affiliate campaigns and maximize your commissions.',
          category: 'Business',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Faffiliate_marketing_guide.pdf?alt=media',
          fileName: 'affiliate_marketing_guide.pdf',
          fileType: 'pdf',
          fileSize: 2048000, // 2MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'business_email_marketing_secrets',
          title: 'Email Marketing Secrets',
          description:
              'Discover the secrets of successful email marketing campaigns. This guide includes templates, strategies, and best practices for building and monetizing your email list.',
          category: 'Business',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Femail_marketing_secrets.pdf?alt=media',
          fileName: 'email_marketing_secrets.pdf',
          fileType: 'pdf',
          fileSize: 1536000, // 1.5MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'business_social_media_mastery',
          title: 'Social Media Marketing Mastery',
          description:
              'Master social media marketing with this comprehensive video course. Learn platform-specific strategies for Facebook, Instagram, Twitter, and LinkedIn.',
          category: 'Business',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fsocial_media_mastery.mp4?alt=media',
          fileName: 'social_media_mastery.mp4',
          fileType: 'mp4',
          fileSize: 52428800, // 50MB
          createdAt: DateTime.now(),
        ),

        // Health Category
        ContentItem(
          id: 'health_nutrition_basics',
          title: 'Nutrition Basics for Healthy Living',
          description:
              'Essential nutrition information for maintaining a healthy lifestyle. Includes meal planning guides, nutritional charts, and healthy recipe ideas.',
          category: 'Health',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fnutrition_basics.pdf?alt=media',
          fileName: 'nutrition_basics.pdf',
          fileType: 'pdf',
          fileSize: 3072000, // 3MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'health_fitness_workout_plan',
          title: '30-Day Fitness Workout Plan',
          description:
              'A complete 30-day workout plan designed for all fitness levels. Includes exercise descriptions, progress tracking sheets, and nutrition tips.',
          category: 'Health',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Ffitness_workout_plan.pdf?alt=media',
          fileName: 'fitness_workout_plan.pdf',
          fileType: 'pdf',
          fileSize: 2560000, // 2.5MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'health_meditation_guide',
          title: 'Meditation and Mindfulness Guide',
          description:
              'Learn the art of meditation and mindfulness with this comprehensive text guide. Includes various meditation techniques and daily practice routines.',
          category: 'Health',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fmeditation_guide.txt?alt=media',
          fileName: 'meditation_guide.txt',
          fileType: 'txt',
          fileSize: 512000, // 512KB
          createdAt: DateTime.now(),
        ),

        // Technology Category
        ContentItem(
          id: 'tech_ai_introduction',
          title: 'Introduction to Artificial Intelligence',
          description:
              'A beginner-friendly introduction to AI concepts, applications, and future trends. Perfect for anyone looking to understand the basics of artificial intelligence.',
          category: 'Technology',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fai_introduction.pdf?alt=media',
          fileName: 'ai_introduction.pdf',
          fileType: 'pdf',
          fileSize: 1792000, // 1.75MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'tech_web_development_basics',
          title: 'Web Development Fundamentals',
          description:
              'Learn the fundamentals of web development including HTML, CSS, and JavaScript. This guide provides a solid foundation for aspiring web developers.',
          category: 'Technology',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fweb_development_basics.pdf?alt=media',
          fileName: 'web_development_basics.pdf',
          fileType: 'pdf',
          fileSize: 4096000, // 4MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'tech_coding_tutorial_video',
          title: 'Python Programming Tutorial',
          description:
              'A comprehensive video tutorial covering Python programming from basics to advanced concepts. Perfect for beginners and intermediate programmers.',
          category: 'Technology',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fpython_tutorial.mp4?alt=media',
          fileName: 'python_tutorial.mp4',
          fileType: 'mp4',
          fileSize: 104857600, // 100MB
          createdAt: DateTime.now(),
        ),

        // Education Category
        ContentItem(
          id: 'education_study_techniques',
          title: 'Effective Study Techniques',
          description:
              'Proven study techniques and methods to improve learning efficiency and retention. Includes memory techniques, note-taking strategies, and exam preparation tips.',
          category: 'Education',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fstudy_techniques.pdf?alt=media',
          fileName: 'study_techniques.pdf',
          fileType: 'pdf',
          fileSize: 1280000, // 1.25MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'education_language_learning',
          title: 'Language Learning Strategies',
          description:
              'Effective strategies for learning new languages quickly and efficiently. Includes practical tips, resources, and practice exercises.',
          category: 'Education',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Flanguage_learning.txt?alt=media',
          fileName: 'language_learning.txt',
          fileType: 'txt',
          fileSize: 768000, // 768KB
          createdAt: DateTime.now(),
        ),

        // Lifestyle Category
        ContentItem(
          id: 'lifestyle_productivity_hacks',
          title: 'Productivity Hacks for Busy People',
          description:
              'Time-saving productivity hacks and life organization tips for busy professionals. Learn how to maximize your efficiency and achieve work-life balance.',
          category: 'Lifestyle',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fproductivity_hacks.pdf?alt=media',
          fileName: 'productivity_hacks.pdf',
          fileType: 'pdf',
          fileSize: 1024000, // 1MB
          createdAt: DateTime.now(),
        ),
        ContentItem(
          id: 'lifestyle_home_organization',
          title: 'Home Organization Masterclass',
          description:
              'Transform your living space with these professional home organization techniques. Includes room-by-room guides and decluttering strategies.',
          category: 'Lifestyle',
          fileUrl:
              'https://firebasestorage.googleapis.com/v0/b/myapp-59f81.firebasestorage.app/o/sample%2Fhome_organization.mp4?alt=media',
          fileName: 'home_organization.mp4',
          fileType: 'mp4',
          fileSize: 73400320, // 70MB
          createdAt: DateTime.now(),
        ),
      ];

      // Add all content items to the database
      await _dbService.addMultipleContentItems(sampleItems);

      print('Successfully created ${sampleItems.length} sample content items!');
      print('Categories created:');
      final Set<String> categories = sampleItems
          .map((item) => item.category)
          .toSet();
      for (final category in categories) {
        final int count = sampleItems
            .where((item) => item.category == category)
            .length;
        print('  - $category: $count items');
      }
    } catch (e) {
      print('Error creating sample content: $e');
      rethrow;
    }
  }

  /// Clear all existing content (use with caution!)
  Future<void> clearAllContent() async {
    try {
      print('Clearing all existing content...');

      // Get all categories
      final List<String> categories = await _dbService.getCategories();

      for (final category in categories) {
        final List<ContentItem> items = await _dbService.getContentByCategory(
          category,
        );
        print('Found ${items.length} items in $category category');

        // Note: This would require implementing a delete method in the database service
        // For now, we'll just print what would be deleted
      }

      print('Content clearing completed');
    } catch (e) {
      print('Error clearing content: $e');
      rethrow;
    }
  }

  /// Test Firebase Storage connection
  Future<void> testFirebaseConnection() async {
    try {
      print('Testing Firebase Storage connection...');

      // Create a simple test file
      final Directory tempDir = Directory.systemTemp;
      final File testFile = File('${tempDir.path}/test_upload.txt');
      await testFile.writeAsString(
        'This is a test file for Firebase Storage upload.',
      );

      print('Created test file: ${testFile.path}');

      // Try to upload the test file
      final String downloadUrl = await _storageService.uploadFile(
        file: testFile,
        category: 'test',
        fileName: 'test_upload.txt',
      );

      print('✅ Test upload successful!');
      print('Download URL: $downloadUrl');

      // Clean up test file
      await testFile.delete();

      // Create a test content item in database
      final ContentItem testItem = ContentItem(
        id: 'test_item_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Test Upload',
        description: 'This is a test upload to verify Firebase connectivity.',
        category: 'Test',
        fileUrl: downloadUrl,
        fileName: 'test_upload.txt',
        fileType: 'txt',
        fileSize: 100,
        createdAt: DateTime.now(),
      );

      await _dbService.addContentItem(testItem);
      print('✅ Test database entry created!');
    } catch (e) {
      print('❌ Firebase test failed: $e');
      rethrow;
    }
  }

  /// Upload real content from the content directory
  Future<void> uploadRealContent() async {
    try {
      print('Starting real content upload...');

      final Directory contentDir = Directory('content');
      if (!await contentDir.exists()) {
        throw Exception('Content directory not found at: ${contentDir.path}');
      }

      print('Content directory found: ${contentDir.path}');

      // Define category mapping
      final Map<String, String> categoryMapping = {
        'AIForProductivity': 'Technology',
        'AffCommissionFormula': 'Business',
        'AffiliateCashMastery': 'Business',
        '12,000 Recipes bonus-20250715T102342Z-1-001': 'Health',
      };

      int totalUploaded = 0;

      // Process each main folder
      await for (final FileSystemEntity entity in contentDir.list()) {
        if (entity is Directory) {
          final String folderName = path.basename(entity.path);
          final String category = categoryMapping[folderName] ?? 'General';

          print('Processing folder: $folderName -> Category: $category');

          final int uploaded = await _processContentFolder(entity, category);
          totalUploaded += uploaded;

          // Limit uploads for demo (remove this in production)
          if (totalUploaded >= 20) {
            print('Limiting to 20 files for demo purposes');
            break;
          }
        }
      }

      print('Successfully uploaded $totalUploaded files!');
    } catch (e) {
      print('Error uploading real content: $e');
      rethrow;
    }
  }

  /// Process a content folder and upload files
  Future<int> _processContentFolder(Directory folder, String category) async {
    int uploadedCount = 0;

    await for (final FileSystemEntity entity in folder.list(recursive: true)) {
      if (entity is File) {
        final String fileName = path.basename(entity.path);
        final String extension = path.extension(fileName).toLowerCase();

        // Skip system files and unsupported formats
        if (_shouldSkipFile(fileName, extension)) {
          continue;
        }

        try {
          print('  Uploading: $fileName');

          // Upload to Firebase Storage
          final String downloadUrl = await _storageService.uploadFile(
            file: entity,
            category: category,
            fileName: fileName,
          );

          // Create content item
          final ContentItem contentItem = _createContentItemFromFile(
            file: entity,
            category: category,
            downloadUrl: downloadUrl,
          );

          // Add to database
          await _dbService.addContentItem(contentItem);

          uploadedCount++;
          print('    ✅ Success');

          // Limit files per folder
          if (uploadedCount >= 5) {
            print('    Limiting to 5 files per folder for demo');
            break;
          }
        } catch (e) {
          print('    ❌ Failed: $e');
        }
      }
    }

    return uploadedCount;
  }

  /// Check if file should be skipped
  bool _shouldSkipFile(String fileName, String extension) {
    // Skip system files
    if (fileName.startsWith('.') || fileName.toLowerCase() == 'desktop.ini') {
      return true;
    }

    // Only upload supported file types
    const supportedExtensions = [
      '.pdf',
      '.mp4',
      '.txt',
      '.doc',
      '.docx',
      '.mov',
      '.avi',
      '.mkv',
    ];
    return !supportedExtensions.contains(extension);
  }

  /// Create ContentItem from file
  ContentItem _createContentItemFromFile({
    required File file,
    required String category,
    required String downloadUrl,
  }) {
    final String fileName = path.basename(file.path);
    final String fileNameWithoutExt = path.basenameWithoutExtension(file.path);
    final String extension = path.extension(file.path);
    final int fileSize = file.lengthSync();

    // Generate unique ID
    final String id = _generateContentId(fileName, category);

    // Create title from filename
    final String title = _generateTitle(fileNameWithoutExt);

    // Generate description
    final String description = _generateDescription(title, category, extension);

    return ContentItem(
      id: id,
      title: title,
      description: description,
      category: category,
      fileUrl: downloadUrl,
      fileName: fileName,
      fileType: extension.replaceFirst('.', ''),
      fileSize: fileSize,
      createdAt: DateTime.now(),
    );
  }

  /// Generate unique content ID
  String _generateContentId(String fileName, String category) {
    final String sanitized = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');

    return '${category.toLowerCase()}_${sanitized}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Generate title from filename
  String _generateTitle(String fileNameWithoutExt) {
    return fileNameWithoutExt
        .replaceAll(RegExp(r'[_-]'), ' ')
        .split(' ')
        .map(
          (word) => word.isNotEmpty
              ? word[0].toUpperCase() + word.substring(1).toLowerCase()
              : word,
        )
        .join(' ')
        .trim();
  }

  /// Generate description
  String _generateDescription(String title, String category, String extension) {
    final String fileType = _getFileTypeDescription(extension);
    return 'A $fileType resource about $title in the $category category. This PLR/MRR content is ready for download and use.';
  }

  /// Get file type description
  String _getFileTypeDescription(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'PDF document';
      case '.mp4':
      case '.mov':
      case '.avi':
      case '.mkv':
        return 'video';
      case '.txt':
        return 'text document';
      case '.doc':
      case '.docx':
        return 'Word document';
      default:
        return 'file';
    }
  }
}
