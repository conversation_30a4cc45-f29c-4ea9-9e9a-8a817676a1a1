import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

/// Content Upload Script for PLR/MRR Flutter App
/// This script uploads content to Firebase Storage and populates Realtime Database
class ContentUploadScript {
  // Firebase configuration
  static const String projectId = 'myapp-59f81';
  static const String storageBucket = 'myapp-59f81.firebasestorage.app';
  static const String databaseUrl = 'https://myapp-59f81-default-rtdb.firebaseio.com';
  static const String apiKey = 'AIzaSyAl9DsCymN6YDDvEXM286bzu7B_pcO_gsY';

  final http.Client _client = http.Client();

  /// Main upload function
  Future<void> uploadAllContent() async {
    print('🚀 Starting PLR Content Upload...');
    
    final Directory contentDir = Directory('content');
    if (!await contentDir.exists()) {
      throw Exception('Content directory not found!');
    }

    // Define category mapping for better organization
    final Map<String, String> categoryMapping = {
      'AIForProductivity': 'Technology',
      'AffCommissionFormula': 'Business',
      'AffiliateCashMastery': 'Business',
      '12,000 Recipes bonus-20250715T102342Z-1-001': 'Health',
    };

    int totalFiles = 0;
    int uploadedFiles = 0;

    // Process each main folder
    await for (final FileSystemEntity entity in contentDir.list()) {
      if (entity is Directory) {
        final String folderName = path.basename(entity.path);
        final String category = categoryMapping[folderName] ?? 'General';
        
        print('\n📁 Processing folder: $folderName -> Category: $category');
        
        final result = await _processFolder(entity, category);
        totalFiles += result['total'] as int;
        uploadedFiles += result['uploaded'] as int;
      }
    }

    print('\n✅ Upload completed!');
    print('📊 Total files processed: $totalFiles');
    print('📤 Successfully uploaded: $uploadedFiles');
    print('❌ Failed uploads: ${totalFiles - uploadedFiles}');
  }

  /// Process a folder and upload its contents
  Future<Map<String, int>> _processFolder(Directory folder, String category) async {
    int totalFiles = 0;
    int uploadedFiles = 0;

    await for (final FileSystemEntity entity in folder.list(recursive: true)) {
      if (entity is File) {
        final String fileName = path.basename(entity.path);
        final String extension = path.extension(fileName).toLowerCase();
        
        // Skip system files and unsupported formats
        if (_shouldSkipFile(fileName, extension)) {
          continue;
        }

        totalFiles++;
        
        try {
          print('  📄 Uploading: $fileName');
          
          // Upload to Firebase Storage
          final String downloadUrl = await _uploadToStorage(entity, category, fileName);
          
          // Add to Realtime Database
          await _addToDatabase(entity, category, fileName, downloadUrl);
          
          uploadedFiles++;
          print('    ✅ Success');
          
          // Add small delay to avoid overwhelming the API
          await Future.delayed(const Duration(milliseconds: 500));
          
        } catch (e) {
          print('    ❌ Failed: $e');
        }
      }
    }

    return {'total': totalFiles, 'uploaded': uploadedFiles};
  }

  /// Check if file should be skipped
  bool _shouldSkipFile(String fileName, String extension) {
    // Skip system files
    if (fileName.startsWith('.') || fileName.toLowerCase() == 'desktop.ini') {
      return true;
    }

    // Only upload supported file types
    const supportedExtensions = ['.pdf', '.mp4', '.txt', '.doc', '.docx', '.mov', '.avi', '.mkv'];
    return !supportedExtensions.contains(extension);
  }

  /// Upload file to Firebase Storage
  Future<String> _uploadToStorage(File file, String category, String fileName) async {
    final List<int> fileBytes = await file.readAsBytes();
    final String encodedFileName = Uri.encodeComponent(fileName);
    final String storagePath = 'content/$category/$encodedFileName';
    
    // Upload to Firebase Storage using REST API
    final String uploadUrl = 'https://firebasestorage.googleapis.com/v0/b/$storageBucket/o?name=$storagePath';
    
    final response = await _client.post(
      Uri.parse(uploadUrl),
      headers: {
        'Content-Type': 'application/octet-stream',
      },
      body: fileBytes,
    );

    if (response.statusCode != 200) {
      throw Exception('Storage upload failed: ${response.body}');
    }

    final Map<String, dynamic> responseData = json.decode(response.body);
    final String downloadToken = responseData['downloadTokens'];
    
    return 'https://firebasestorage.googleapis.com/v0/b/$storageBucket/o/$storagePath?alt=media&token=$downloadToken';
  }

  /// Add content item to Realtime Database
  Future<void> _addToDatabase(File file, String category, String fileName, String downloadUrl) async {
    final String fileNameWithoutExt = path.basenameWithoutExtension(fileName);
    final String extension = path.extension(fileName);
    final int fileSize = await file.length();
    
    // Generate content item data
    final Map<String, dynamic> contentItem = {
      'title': _generateTitle(fileNameWithoutExt),
      'description': _generateDescription(fileNameWithoutExt, category, extension),
      'category': category,
      'fileUrl': downloadUrl,
      'fileName': fileName,
      'fileType': extension.replaceFirst('.', ''),
      'fileSize': fileSize,
      'createdAt': DateTime.now().toIso8601String(),
    };

    // Generate unique ID
    final String contentId = _generateContentId(fileName, category);
    
    // Add to database
    final String dbUrl = '$databaseUrl/content/$category/$contentId.json';
    
    final response = await _client.put(
      Uri.parse(dbUrl),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(contentItem),
    );

    if (response.statusCode != 200) {
      throw Exception('Database update failed: ${response.body}');
    }
  }

  /// Generate a clean title from filename
  String _generateTitle(String fileNameWithoutExt) {
    return fileNameWithoutExt
        .replaceAll(RegExp(r'[_-]'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' ')
        .trim();
  }

  /// Generate description based on content
  String _generateDescription(String title, String category, String extension) {
    final String fileType = _getFileTypeDescription(extension);
    return 'A $fileType resource about $title in the $category category. This PLR/MRR content is ready for download and use.';
  }

  /// Get file type description
  String _getFileTypeDescription(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'PDF document';
      case '.mp4':
      case '.mov':
      case '.avi':
      case '.mkv':
        return 'video';
      case '.txt':
        return 'text document';
      case '.doc':
      case '.docx':
        return 'Word document';
      default:
        return 'file';
    }
  }

  /// Generate unique content ID
  String _generateContentId(String fileName, String category) {
    final String sanitized = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');
    
    return '${category.toLowerCase()}_${sanitized}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Clean up resources
  void dispose() {
    _client.close();
  }
}

/// Main function to run the upload script
void main() async {
  final ContentUploadScript uploader = ContentUploadScript();
  
  try {
    await uploader.uploadAllContent();
  } catch (e) {
    print('💥 Upload failed: $e');
    exit(1);
  } finally {
    uploader.dispose();
  }
  
  print('🎉 All done!');
}
