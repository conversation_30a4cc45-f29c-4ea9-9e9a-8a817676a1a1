import 'dart:io';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/models.dart';
import 'realtime_database_service.dart';

class DownloadService {
  static final DownloadService _instance = DownloadService._internal();
  factory DownloadService() => _instance;
  DownloadService._internal();

  final Dio _dio = Dio();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  /// Download a content item to local storage
  Future<String> downloadContent({
    required ContentItem content,
    Function(double progress)? onProgress,
  }) async {
    try {
      // Check if already downloaded
      final bool alreadyDownloaded = await _dbService.hasUserDownloaded(
        content.id,
      );
      if (alreadyDownloaded) {
        final List<UserDownload> downloads = await _dbService
            .getUserDownloads();
        final UserDownload? existingDownload =
            downloads.where((d) => d.contentId == content.id).isNotEmpty
            ? downloads.where((d) => d.contentId == content.id).first
            : null;

        if (existingDownload != null &&
            await File(existingDownload.localPath).exists()) {
          return existingDownload.localPath;
        }
      }

      // Request storage permission
      await _requestStoragePermission();

      // Get download directory
      final Directory downloadDir = await _getDownloadDirectory();

      // Create category subdirectory
      final Directory categoryDir = Directory(
        '${downloadDir.path}/${content.category}',
      );
      if (!await categoryDir.exists()) {
        await categoryDir.create(recursive: true);
      }

      // Create local file path
      final String localPath = '${categoryDir.path}/${content.fileName}';

      // Download file
      await _dio.download(
        content.fileUrl,
        localPath,
        onReceiveProgress: (received, total) {
          if (onProgress != null && total != -1) {
            final double progress = received / total;
            onProgress(progress);
          }
        },
      );

      // Create user download record
      final UserDownload userDownload = UserDownload(
        contentId: content.id,
        title: content.title,
        category: content.category,
        fileUrl: content.fileUrl,
        fileName: content.fileName,
        localPath: localPath,
        downloadedAt: DateTime.now(),
        fileSize: content.fileSize,
        isDownloaded: true,
      );

      // Save download record to database
      await _dbService.addUserDownload(userDownload);

      return localPath;
    } catch (e) {
      throw Exception('Failed to download content: $e');
    }
  }

  /// Check if content is already downloaded
  Future<bool> isContentDownloaded(String contentId) async {
    try {
      final bool hasRecord = await _dbService.hasUserDownloaded(contentId);
      if (!hasRecord) return false;

      final List<UserDownload> downloads = await _dbService.getUserDownloads();
      final UserDownload? download =
          downloads.where((d) => d.contentId == contentId).isNotEmpty
          ? downloads.where((d) => d.contentId == contentId).first
          : null;

      if (download == null) return false;

      // Check if file actually exists locally
      final bool fileExists = await File(download.localPath).exists();

      if (!fileExists) {
        // Remove stale record
        await _dbService.removeUserDownload(contentId);
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get local path for downloaded content
  Future<String?> getLocalPath(String contentId) async {
    try {
      final List<UserDownload> downloads = await _dbService.getUserDownloads();
      final UserDownload? download =
          downloads.where((d) => d.contentId == contentId).isNotEmpty
          ? downloads.where((d) => d.contentId == contentId).first
          : null;

      if (download == null) return null;

      // Verify file exists
      if (await File(download.localPath).exists()) {
        return download.localPath;
      } else {
        // Remove stale record
        await _dbService.removeUserDownload(contentId);
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Delete downloaded content from local storage
  Future<void> deleteDownloadedContent(String contentId) async {
    try {
      final String? localPath = await getLocalPath(contentId);
      if (localPath != null) {
        final File file = File(localPath);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // Remove from database
      await _dbService.removeUserDownload(contentId);
    } catch (e) {
      throw Exception('Failed to delete downloaded content: $e');
    }
  }

  /// Get total size of all downloaded files
  Future<int> getTotalDownloadSize() async {
    try {
      final List<UserDownload> downloads = await _dbService.getUserDownloads();
      int totalSize = 0;

      for (final download in downloads) {
        if (await File(download.localPath).exists()) {
          totalSize += download.fileSize;
        }
      }

      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Clear all downloaded content
  Future<void> clearAllDownloads() async {
    try {
      final List<UserDownload> downloads = await _dbService.getUserDownloads();

      for (final download in downloads) {
        try {
          final File file = File(download.localPath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          // Continue with other files even if one fails
          // Continue with other files even if one fails
        }
      }

      // Clear all download records from database
      final List<UserDownload> allDownloads = await _dbService
          .getUserDownloads();
      for (final download in allDownloads) {
        await _dbService.removeUserDownload(download.contentId);
      }
    } catch (e) {
      throw Exception('Failed to clear all downloads: $e');
    }
  }

  /// Request storage permission
  Future<void> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final PermissionStatus status = await Permission.storage.request();
      if (status != PermissionStatus.granted) {
        throw Exception('Storage permission denied');
      }
    }
  }

  /// Get download directory
  Future<Directory> _getDownloadDirectory() async {
    if (Platform.isAndroid) {
      // Use external storage for Android
      final Directory? externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        final Directory downloadDir = Directory(
          '${externalDir.path}/PLR_Downloads',
        );
        if (!await downloadDir.exists()) {
          await downloadDir.create(recursive: true);
        }
        return downloadDir;
      }
    }

    // Fallback to documents directory
    final Directory documentsDir = await getApplicationDocumentsDirectory();
    final Directory downloadDir = Directory(
      '${documentsDir.path}/PLR_Downloads',
    );
    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }
    return downloadDir;
  }
}
