import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Check if storage permissions are granted
  Future<bool> hasStoragePermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final int sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        // Android 13+ - Check media permissions
        final photos = await Permission.photos.status;
        final videos = await Permission.videos.status;
        final audio = await Permission.audio.status;

        return photos.isGranted && videos.isGranted && audio.isGranted;
      } else if (sdkInt >= 30) {
        // Android 11-12 - Check manage external storage or regular storage
        final manageStatus = await Permission.manageExternalStorage.status;
        if (manageStatus.isGranted) return true;

        final storageStatus = await Permission.storage.status;
        return storageStatus.isGranted;
      } else {
        // Android 10 and below - Regular storage permission
        final status = await Permission.storage.status;
        return status.isGranted;
      }
    } catch (e) {
      return false;
    }
  }

  /// Request storage permissions with user-friendly dialog
  Future<bool> requestStoragePermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final int sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        // Android 13+ - Request specific media permissions
        final Map<Permission, PermissionStatus> statuses = await [
          Permission.photos,
          Permission.videos,
          Permission.audio,
        ].request();

        return statuses.values.every(
          (status) =>
              status == PermissionStatus.granted ||
              status == PermissionStatus.limited,
        );
      } else if (sdkInt >= 30) {
        // Android 11-12 - Try MANAGE_EXTERNAL_STORAGE first
        final manageStatus = await Permission.manageExternalStorage.request();
        if (manageStatus.isGranted) return true;

        // Fallback to regular storage permission
        final storageStatus = await Permission.storage.request();
        return storageStatus.isGranted;
      } else {
        // Android 10 and below - Regular storage permission
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } catch (e) {
      return false;
    }
  }

  /// Show permission rationale dialog
  Future<bool> shouldShowPermissionRationale() async {
    if (!Platform.isAndroid) return false;

    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final int sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        return await Permission.photos.shouldShowRequestRationale ||
            await Permission.videos.shouldShowRequestRationale ||
            await Permission.audio.shouldShowRequestRationale;
      } else {
        return await Permission.storage.shouldShowRequestRationale;
      }
    } catch (e) {
      return false;
    }
  }

  /// Open app settings for manual permission grant
  Future<void> openPermissionSettings() async {
    await openAppSettings();
  }

  /// Get permission status description for user
  String getPermissionStatusDescription() {
    return Platform.isAndroid
        ? "Storage permissions are required to download and save PLR content to your device."
        : "File access is required to save downloaded content.";
  }

  /// Get detailed permission requirements based on Android version
  Future<String> getPermissionRequirements() async {
    if (!Platform.isAndroid) {
      return "File access permissions are required to save downloaded content.";
    }

    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final int sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        return "Media permissions (Photos, Videos, Audio) are required to save downloaded PLR content to your device.";
      } else if (sdkInt >= 30) {
        return "Storage permissions are required to save downloaded PLR content to your device. You may need to grant 'All files access' permission.";
      } else {
        return "Storage permissions are required to save downloaded PLR content to your device.";
      }
    } catch (e) {
      return "Storage permissions are required to save downloaded content.";
    }
  }
}
