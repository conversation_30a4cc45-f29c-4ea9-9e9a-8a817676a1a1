// Node.js script to upload content to Firebase
// Run: node upload_script.js

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
// You'll need to download the service account key from Firebase Console
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  storageBucket: 'myapp-59f81.firebasestorage.app',
  databaseURL: 'https://myapp-59f81-default-rtdb.firebaseio.com'
});

const bucket = admin.storage().bucket();
const db = admin.database();

async function uploadContent() {
  console.log('🚀 Starting content upload...');
  
  const contentDir = './content';
  const categoryMapping = {
    'AIForProductivity': 'Technology',
    'AffCommissionFormula': 'Business',
    'AffiliateCashMastery': 'Business',
    '12,000 Recipes bonus-20250715T102342Z-1-001': 'Health',
  };

  try {
    const folders = fs.readdirSync(contentDir);
    
    for (const folder of folders) {
      const folderPath = path.join(contentDir, folder);
      if (fs.statSync(folderPath).isDirectory()) {
        const category = categoryMapping[folder] || 'General';
        console.log(`\n📁 Processing: ${folder} -> ${category}`);
        
        await uploadFolder(folderPath, category);
      }
    }
    
    console.log('✅ Upload completed!');
  } catch (error) {
    console.error('❌ Upload failed:', error);
  }
}

async function uploadFolder(folderPath, category) {
  const files = getAllFiles(folderPath);
  let uploadCount = 0;
  
  for (const filePath of files) {
    if (uploadCount >= 5) break; // Limit for demo
    
    const fileName = path.basename(filePath);
    const extension = path.extname(fileName).toLowerCase();
    
    // Skip unsupported files
    if (!['.pdf', '.mp4', '.txt', '.doc', '.docx'].includes(extension)) {
      continue;
    }
    
    try {
      console.log(`  📄 Uploading: ${fileName}`);
      
      // Upload to Storage
      const destination = `content/${category}/${fileName}`;
      await bucket.upload(filePath, { destination });
      
      // Get download URL
      const file = bucket.file(destination);
      const [url] = await file.getSignedUrl({
        action: 'read',
        expires: '03-09-2491'
      });
      
      // Add to Realtime Database
      const contentItem = {
        title: generateTitle(path.parse(fileName).name),
        description: `A ${extension.slice(1)} resource in the ${category} category.`,
        category: category,
        fileUrl: url,
        fileName: fileName,
        fileType: extension.slice(1),
        fileSize: fs.statSync(filePath).size,
        createdAt: new Date().toISOString()
      };
      
      const contentId = generateId(fileName, category);
      await db.ref(`content/${category}/${contentId}`).set(contentItem);
      
      uploadCount++;
      console.log(`    ✅ Success`);
      
    } catch (error) {
      console.log(`    ❌ Failed: ${error.message}`);
    }
  }
}

function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });
  
  return arrayOfFiles;
}

function generateTitle(filename) {
  return filename
    .replace(/[_-]/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

function generateId(fileName, category) {
  const sanitized = fileName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
  
  return `${category.toLowerCase()}_${sanitized}_${Date.now()}`;
}

// Run the upload
uploadContent();
