-- Merging decision tree log ---
application
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-20:19
MERGED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c2fa1f3b7d3b25c1b9daa36ef13fb3a\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c2fa1f3b7d3b25c1b9daa36ef13fb3a\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3649848b2b11dd9a2b9a378f9ad41451\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3649848b2b11dd9a2b9a378f9ad41451\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a6667b07538661e3337019a9268efa0\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a6667b07538661e3337019a9268efa0\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c911b2bb9ac96af621f362c1e94d1d78\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c911b2bb9ac96af621f362c1e94d1d78\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f448994e490bf59f20aa19226509e50\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a06d06ec0ce1c73cc8df2ae11f0f21f\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c2fa1f3b7d3b25c1b9daa36ef13fb3a\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [:google_sign_in_android] E:\Fiverr_Projects\my_flutter_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] E:\Fiverr_Projects\my_flutter_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] E:\Fiverr_Projects\my_flutter_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] E:\Fiverr_Projects\my_flutter_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3649848b2b11dd9a2b9a378f9ad41451\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a6667b07538661e3337019a9268efa0\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3593fbad8c8f81433aca088ae6827b41\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c911b2bb9ac96af621f362c1e94d1d78\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c2bb4cddb87481b6b754c0fdd3839a9b\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f660676bf4ab7e115492941cf8444d98\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a0f53fd92f6561a1dd451c29da9990\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\36c146f9d5b29261e8023a68def6da94\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\25a37e5cf0f4e5220cbf7cafe9249990\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:23:5-67
MERGED from [:google_sign_in_android] E:\Fiverr_Projects\my_flutter_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] E:\Fiverr_Projects\my_flutter_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
	android:name
		ADDED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f448994e490bf59f20aa19226509e50\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0f448994e490bf59f20aa19226509e50\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a06d06ec0ce1c73cc8df2ae11f0f21f\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a06d06ec0ce1c73cc8df2ae11f0f21f\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c2fa1f3b7d3b25c1b9daa36ef13fb3a\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c2fa1f3b7d3b25c1b9daa36ef13fb3a\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [:google_sign_in_android] E:\Fiverr_Projects\my_flutter_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] E:\Fiverr_Projects\my_flutter_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] E:\Fiverr_Projects\my_flutter_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] E:\Fiverr_Projects\my_flutter_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] E:\Fiverr_Projects\my_flutter_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] E:\Fiverr_Projects\my_flutter_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] E:\Fiverr_Projects\my_flutter_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] E:\Fiverr_Projects\my_flutter_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14c47a061c0f4340fe3cb3b4b456289f\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecd3d9e1ce1f4362a391d5da65b7fedd\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd9b90c84d90ecc5b8d81064f42a0f6\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3649848b2b11dd9a2b9a378f9ad41451\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3649848b2b11dd9a2b9a378f9ad41451\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a6667b07538661e3337019a9268efa0\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a6667b07538661e3337019a9268efa0\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3593fbad8c8f81433aca088ae6827b41\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3593fbad8c8f81433aca088ae6827b41\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\faeaeacf92c1b16df86d680ce0e0019c\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c911b2bb9ac96af621f362c1e94d1d78\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c911b2bb9ac96af621f362c1e94d1d78\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c2bb4cddb87481b6b754c0fdd3839a9b\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c2bb4cddb87481b6b754c0fdd3839a9b\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f660676bf4ab7e115492941cf8444d98\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f660676bf4ab7e115492941cf8444d98\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a0f53fd92f6561a1dd451c29da9990\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a0f53fd92f6561a1dd451c29da9990\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\36c146f9d5b29261e8023a68def6da94\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\36c146f9d5b29261e8023a68def6da94\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\25a37e5cf0f4e5220cbf7cafe9249990\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\25a37e5cf0f4e5220cbf7cafe9249990\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Fiverr_Projects\my_flutter_app\android\app\src\debug\AndroidManifest.xml
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-18:53
	android:resource
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-50
	android:name
		ADDED from [:open_file_android] E:\Fiverr_Projects\my_flutter_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-67
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] E:\Fiverr_Projects\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar
ADDED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_database] E:\Fiverr_Projects\my_flutter_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_storage] E:\Fiverr_Projects\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] E:\Fiverr_Projects\my_flutter_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.my_flutter_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
