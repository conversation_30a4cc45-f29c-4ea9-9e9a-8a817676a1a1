<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="default_web_client_id" translatable="false">664484978086-4olunm90dbg2tpf57552mr7svpr66pdf.apps.googleusercontent.com</string>
    <string name="firebase_database_url" translatable="false">https://myapp-59f81-default-rtdb.firebaseio.com</string>
    <string name="gcm_defaultSenderId" translatable="false">664484978086</string>
    <string name="google_api_key" translatable="false">AIzaSyAl9DsCymN6YDDvEXM286bzu7B_pcO_gsY</string>
    <string name="google_app_id" translatable="false">1:664484978086:android:9c86781d7d2647cd31c367</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAl9DsCymN6YDDvEXM286bzu7B_pcO_gsY</string>
    <string name="google_storage_bucket" translatable="false">myapp-59f81.firebasestorage.app</string>
    <string name="project_id" translatable="false">myapp-59f81</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>