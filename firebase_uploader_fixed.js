// Firebase Content Uploader - Fixed Authentication
// Run: node firebase_uploader_fixed.js

const admin = require("firebase-admin");
const fs = require("fs");
const path = require("path");

// Initialize Firebase Admin SDK with Application Default Credentials
console.log("🔧 Initializing Firebase...");

// Try to use Application Default Credentials first
try {
  // Set the Google Application Credentials environment variable
  process.env.GOOGLE_APPLICATION_CREDENTIALS = path.join(
    __dirname,
    "serviceAccountKey.json"
  );

  admin.initializeApp({
    projectId: "myapp-59f81",
    storageBucket: "myapp-59f81.firebasestorage.app",
    databaseURL: "https://myapp-59f81-default-rtdb.firebaseio.com",
  });

  console.log("✅ Firebase initialized with service account");
} catch (error) {
  console.log("⚠️ Service account not found, trying alternative method...");

  // Alternative: Initialize without credentials (will use Firebase CLI auth)
  try {
    admin.initializeApp({
      projectId: "myapp-59f81",
      storageBucket: "myapp-59f81.firebasestorage.app",
      databaseURL: "https://myapp-59f81-default-rtdb.firebaseio.com",
      credential: admin.credential.applicationDefault(),
    });
    console.log("✅ Firebase initialized with application default credentials");
  } catch (error2) {
    console.error("❌ Firebase initialization failed:", error2.message);
    console.log("\n🔧 To fix this issue:");
    console.log("1. Download service account key from Firebase Console");
    console.log("2. Save it as 'serviceAccountKey.json' in this directory");
    console.log("3. Or run: gcloud auth application-default login");
    process.exit(1);
  }
}

const bucket = admin.storage().bucket();
const db = admin.database();

// Configuration
const CONFIG = {
  contentDir: "./content",
  maxFilesPerCategory: 999999, // Upload ALL files
  maxTotalFiles: 999999, // Upload ALL files
  supportedExtensions: [
    ".pdf",
    ".mp4",
    ".txt",
    ".doc",
    ".docx",
    ".mov",
    ".avi",
    ".mkv",
  ],
  categoryMapping: {
    AIForProductivity: "Technology",
    AffCommissionFormula: "Business",
    AffiliateCashMastery: "Business",
    "12,000 Recipes bonus-20250715T102342Z-1-001": "Health",
  },
};

// Main upload function
async function uploadAllContent() {
  console.log("🚀 Starting PLR content upload...");

  // Check if content directory exists
  if (!fs.existsSync(CONFIG.contentDir)) {
    console.error(`❌ Content directory not found: ${CONFIG.contentDir}`);
    return;
  }

  let totalUploaded = 0;

  try {
    const folders = fs.readdirSync(CONFIG.contentDir);
    console.log(`📂 Found ${folders.length} folders in content directory`);

    for (const folder of folders) {
      if (totalUploaded >= CONFIG.maxTotalFiles) {
        console.log(
          `⚠️ Reached maximum total file limit (${CONFIG.maxTotalFiles})`
        );
        break;
      }

      const folderPath = path.join(CONFIG.contentDir, folder);
      if (fs.statSync(folderPath).isDirectory()) {
        const category = CONFIG.categoryMapping[folder] || "General";
        console.log(`\n📁 Processing: ${folder} -> ${category}`);

        const uploaded = await uploadCategory(folderPath, category);
        totalUploaded += uploaded;
      }
    }

    console.log(
      `\n🎉 Upload completed! Total files uploaded: ${totalUploaded}`
    );
  } catch (error) {
    console.error("❌ Upload failed:", error);
  }
}

// Upload files from a category folder
async function uploadCategory(folderPath, category) {
  const files = getAllFiles(folderPath);
  let uploadCount = 0;
  let skippedCount = 0;

  console.log(`  📂 Found ${files.length} files in folder`);

  for (const filePath of files) {
    if (uploadCount >= CONFIG.maxFilesPerCategory) {
      console.log(
        `  ⚠️ Reached file limit for ${category} (${CONFIG.maxFilesPerCategory})`
      );
      break;
    }

    const fileName = path.basename(filePath);
    const extension = path.extname(fileName).toLowerCase();

    // Skip system files and unsupported files
    if (shouldSkipFile(fileName, extension)) {
      skippedCount++;
      continue;
    }

    try {
      console.log(`  📄 Uploading: ${fileName}`);

      // Upload to Firebase Storage
      const downloadUrl = await uploadToStorage(filePath, category, fileName);

      // Add to Realtime Database
      await addToDatabase(filePath, category, fileName, downloadUrl);

      uploadCount++;
      console.log(`    ✅ Success (${uploadCount} uploaded)`);

      // Small delay to avoid overwhelming the API (reduced for faster upload)
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.log(`    ❌ Failed: ${error.message}`);
    }
  }

  console.log(
    `  📊 Uploaded ${uploadCount} files from ${category} (${skippedCount} skipped)`
  );
  return uploadCount;
}

// Upload file to Firebase Storage
async function uploadToStorage(filePath, category, fileName) {
  const destination = `content/${category}/${fileName}`;

  const [file] = await bucket.upload(filePath, {
    destination,
    metadata: {
      contentType: getContentType(path.extname(fileName)),
      metadata: {
        category: category,
        uploadedAt: new Date().toISOString(),
      },
    },
  });

  // Make file publicly readable
  await file.makePublic();

  // Return public URL
  return `https://storage.googleapis.com/${bucket.name}/${destination}`;
}

// Add content item to Realtime Database
async function addToDatabase(filePath, category, fileName, downloadUrl) {
  const stats = fs.statSync(filePath);
  const fileNameWithoutExt = path.parse(fileName).name;
  const extension = path.extname(fileName);

  const contentItem = {
    title: generateTitle(fileNameWithoutExt),
    description: generateDescription(fileNameWithoutExt, category, extension),
    category: category,
    fileUrl: downloadUrl,
    fileName: fileName,
    fileType: extension.slice(1),
    fileSize: stats.size,
    createdAt: new Date().toISOString(),
  };

  const contentId = generateId(fileName, category);
  await db.ref(`content/${category}/${contentId}`).set(contentItem);
}

// Helper functions
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

function shouldSkipFile(fileName, extension) {
  // Skip system files
  if (fileName.startsWith(".") || fileName.toLowerCase() === "desktop.ini") {
    return true;
  }

  // Only upload supported file types
  return !CONFIG.supportedExtensions.includes(extension);
}

function getContentType(extension) {
  const types = {
    ".pdf": "application/pdf",
    ".mp4": "video/mp4",
    ".txt": "text/plain",
    ".doc": "application/msword",
    ".docx":
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".mov": "video/quicktime",
    ".avi": "video/x-msvideo",
    ".mkv": "video/x-matroska",
  };
  return types[extension] || "application/octet-stream";
}

function generateTitle(fileNameWithoutExt) {
  return fileNameWithoutExt
    .replace(/[_-]/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ")
    .trim();
}

function generateDescription(title, category, extension) {
  const fileType = getFileTypeDescription(extension);
  return `A ${fileType} resource about ${title} in the ${category} category. This PLR/MRR content is ready for download and use.`;
}

function getFileTypeDescription(extension) {
  const descriptions = {
    ".pdf": "PDF document",
    ".mp4": "video",
    ".mov": "video",
    ".avi": "video",
    ".mkv": "video",
    ".txt": "text document",
    ".doc": "Word document",
    ".docx": "Word document",
  };
  return descriptions[extension] || "file";
}

function generateId(fileName, category) {
  const sanitized = fileName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "_")
    .replace(/_+/g, "_")
    .replace(/^_|_$/g, "");

  return `${category.toLowerCase()}_${sanitized}_${Date.now()}`;
}

// Run the upload
uploadAllContent()
  .then(() => {
    console.log("🏁 Script completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Script failed:", error);
    process.exit(1);
  });
